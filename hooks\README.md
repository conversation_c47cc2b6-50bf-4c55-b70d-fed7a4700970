# 📱 Device Detection & Responsive Hooks

Shared React hooks for device detection, responsive design, and media queries in the social-media monorepo.

## 🚀 Installation

```tsx
// Import from the hooks directory
import { useDeviceDetection, useIsMobile } from '@social-media/hooks'
// or
import { useDeviceDetection, useIsMobile } from '@hooks'
```

## 📋 Available Hooks

### Device Detection Hooks

#### `useDeviceDetection(customBreakpoints?)`
Main hook that returns comprehensive device information.

```tsx
const {
  deviceType,        // 'mobile' | 'tablet' | 'desktop'
  isMobile,         // boolean
  isTablet,         // boolean  
  isDesktop,        // boolean
  screenWidth,      // number
  screenHeight,     // number
  orientation,      // 'portrait' | 'landscape'
  hasTouch,         // boolean
  isStandalone,     // boolean (PWA)
  userAgent         // string
} = useDeviceDetection()
```

#### Simplified Hooks
```tsx
const deviceType = useDeviceType()           // 'mobile' | 'tablet' | 'desktop'
const isMobile = useIsMobile()               // boolean
const isTablet = useIsTablet()               // boolean
const isDesktop = useIsDesktop()             // boolean
const hasTouch = useHasTouch()               // boolean
const isStandalone = useIsStandalone()       // boolean
```

### Media Query Hooks

#### `useMediaQuery(query)`
Generic hook for any CSS media query.

```tsx
const isMobile = useMediaQuery('(max-width: 768px)')
const isDarkMode = useMediaQuery('(prefers-color-scheme: dark)')
const isLandscape = useMediaQuery('(orientation: landscape)')
```

#### Predefined Media Query Hooks
```tsx
// Screen sizes
const isMobileScreen = useIsMobileScreen()     // max-width: 767px
const isTabletScreen = useIsTabletScreen()     // 768px - 1023px
const isDesktopScreen = useIsDesktopScreen()   // min-width: 1024px
const isLargeScreen = useIsLargeScreen()       // min-width: 1440px

// Orientation
const isPortrait = useIsPortrait()
const isLandscape = useIsLandscape()

// System preferences
const prefersDarkMode = usePrefersDarkMode()
const prefersReducedMotion = usePrefersReducedMotion()

// Device capabilities
const canHover = useCanHover()
const hasCoarsePointer = useHasCoarsePointer()
```

#### Composite Hooks
```tsx
const responsive = useResponsive()  // Returns all responsive states
const breakpoint = useBreakpoint()  // 'xs' | 'sm' | 'md' | 'lg' | 'xl'
```

## 🎯 Usage Examples

### Basic Device Detection
```tsx
import { useDeviceDetection } from '@social-media/hooks'

const MyComponent = () => {
  const { isMobile, isTablet, isDesktop } = useDeviceDetection()
  
  return (
    <div>
      {isMobile && <MobileLayout />}
      {isTablet && <TabletLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  )
}
```

### Custom Breakpoints
```tsx
const device = useDeviceDetection({
  mobile: 640,   // 0-639px = mobile
  tablet: 1280,  // 640-1279px = tablet
  desktop: 1280  // 1280px+ = desktop
})
```

### Responsive Components
```tsx
import { useIsMobile, useMediaQuery } from '@social-media/hooks'

const ResponsiveComponent = () => {
  const isMobile = useIsMobile()
  const isLargeScreen = useMediaQuery('(min-width: 1440px)')
  
  return (
    <div className={`
      ${isMobile ? 'p-4' : 'p-8'}
      ${isLargeScreen ? 'max-w-7xl' : 'max-w-4xl'}
    `}>
      Content
    </div>
  )
}
```

### Conditional Rendering
```tsx
import { useHasTouch, useCanHover } from '@social-media/hooks'

const InteractiveElement = () => {
  const hasTouch = useHasTouch()
  const canHover = useCanHover()
  
  return (
    <button
      className={`
        ${hasTouch ? 'py-4 px-6' : 'py-2 px-4'}
        ${canHover ? 'hover:bg-blue-500' : ''}
      `}
    >
      {hasTouch ? 'Tap me' : 'Click me'}
    </button>
  )
}
```

## 🔧 Default Breakpoints

```tsx
{
  mobile: 768,   // 0 - 767px
  tablet: 1024,  // 768 - 1023px  
  desktop: 1024  // 1024px+
}
```

## 🌟 Features

- ✅ **SSR Safe**: Works with Next.js and other SSR frameworks
- ✅ **TypeScript**: Full TypeScript support with proper types
- ✅ **Performance**: Optimized with proper cleanup and memoization
- ✅ **Flexible**: Custom breakpoints and media queries
- ✅ **Comprehensive**: Device detection + media queries + system preferences
- ✅ **Real-time**: Updates on resize, orientation change, etc.

## 🧪 Testing

Use the demo component to test all hooks:

```tsx
import { DeviceDetectionDemo } from '@hooks/demo'

// Add to your Storybook or test page
<DeviceDetectionDemo />
```

## 📱 Mobile DateRangePicker Integration

Perfect for creating adaptive components:

```tsx
import { useIsMobile } from '@social-media/hooks'
import { DateRangePicker } from '@social-media/organisms'
import { MobileDateRangePicker } from './MobileDateRangePicker'

const AdaptiveDateRangePicker = (props) => {
  const isMobile = useIsMobile()
  
  return isMobile ? (
    <MobileDateRangePicker {...props} />
  ) : (
    <DateRangePicker {...props} />
  )
}
```
