{"name": "@social-media/source", "version": "0.0.0", "license": "MIT", "scripts": {"create:ui": "node create-ui.js", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:single": "prettier --write", "storybook": "storybook dev -p 6007 -c .storybook", "build-storybook": "storybook build -c .storybook", "dating:dev": "nx run dating-app:dev"}, "private": true, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@nx/nest": "^21.0.3", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.2", "boxen": "^5.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns-tz": "^3.2.0", "framer-motion": "^12.11.3", "html2canvas": "^1.4.1", "inquirer": "^8.2.5", "lucide-react": "^0.510.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "next": "14.2.15", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.0.0-insiders.d539a72", "react": "18.3.1", "react-dom": "18.3.1", "sass": "^1.89.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@next/eslint-plugin-next": "^15.2.4", "@nx/eslint": "21.0.3", "@nx/eslint-plugin": "21.0.3", "@nx/jest": "21.0.3", "@nx/js": "21.0.3", "@nx/next": "21.0.3", "@nx/react": "21.0.3", "@nx/storybook": "21.0.3", "@nx/vite": "21.0.3", "@nx/web": "21.0.3", "@nx/workspace": "21.0.3", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/core-server": "8.6.12", "@storybook/csf-tools": "^8.6.12", "@storybook/jest": "^0.2.3", "@storybook/nextjs": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test-runner": "^0.22.0", "@storybook/testing-library": "^0.2.2", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/glob": "^8.1.0", "@types/jest": "^29.5.12", "@types/node": "^20.17.46", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.13", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nx": "21.0.3", "postcss": "^8.4.38", "prettier": "^2.6.2", "storybook": "8.6.12", "tailwindcss": "^3.4.3", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.0"}, "workspaces": ["apps/*", "libs/*"], "importSort": {".js": {"style": "module"}}}