/* File: libs/organisms/src/dateRangePicker/dateRangePicker.module.css */

/* Custom styles for DateRangePicker */
.dateRangePicker {
  /* Add any custom styles here if needed */
}

/* Override react-day-picker styles for better range selection */
.dateRangePicker :global(.rdp-day_range_middle) {
  background-color: rgb(var(--primary) / 0.1);
  color: rgb(var(--primary));
  border-radius: 0;
}

.dateRangePicker :global(.rdp-day_range_start) {
  background-color: rgb(var(--primary));
  color: white;
  border-radius: 0.375rem 0 0 0.375rem;
}

.dateRangePicker :global(.rdp-day_range_end) {
  background-color: rgb(var(--primary));
  color: white;
  border-radius: 0 0.375rem 0.375rem 0;
}

.dateRangePicker :global(.rdp-day_range_start.rdp-day_range_end) {
  border-radius: 0.375rem;
}

/* Hover effects */
.dateRangePicker
  :global(.rdp-day:hover:not(.rdp-day_selected):not(.rdp-day_disabled)) {
  background-color: rgb(var(--accent));
  color: rgb(var(--accent-foreground));
}

/* Today indicator - improved styling */
.dateRangePicker :global(.rdp-day_today:not(.rdp-day_selected)) {
  font-weight: bold;
  color: rgb(var(--primary));
  border: 2px solid rgb(var(--primary));
  background-color: white;
  position: relative;
}

/* Today dot indicator */
.dateRangePicker :global(.rdp-day_today:not(.rdp-day_selected))::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: rgb(var(--primary));
  border-radius: 50%;
}

/* Disabled days */
.dateRangePicker :global(.rdp-day_disabled) {
  color: rgb(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}
