<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DateRangePicker Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/react-day-picker@8.10.1/dist/index.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/react-day-picker@8.10.1/dist/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>DateRangePicker Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic DayPicker Range Selection</h2>
        <div id="test1"></div>
        <div id="debug1" class="debug-info"></div>
    </div>

    <script type="text/babel">
        const { useState } = React;
        const { DayPicker } = ReactDayPicker;

        function Test1() {
            const [range, setRange] = useState();

            const handleSelect = (selectedRange) => {
                console.log('Test1 - Selected range:', selectedRange);
                setRange(selectedRange);
                
                // Update debug info
                const debugEl = document.getElementById('debug1');
                debugEl.innerHTML = `
                    <strong>Selected Range:</strong><br>
                    From: ${selectedRange?.from ? selectedRange.from.toISOString() : 'undefined'}<br>
                    To: ${selectedRange?.to ? selectedRange.to.toISOString() : 'undefined'}<br>
                    JSON: ${JSON.stringify(selectedRange, null, 2)}
                `;
            };

            return React.createElement(DayPicker, {
                mode: 'range',
                selected: range,
                onSelect: handleSelect,
                numberOfMonths: 2
            });
        }

        ReactDOM.render(React.createElement(Test1), document.getElementById('test1'));
    </script>

    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</body>
</html>
