import React, { useState } from 'react'
import { DateRange, DayPicker } from 'react-day-picker'

export const TestSimpleRange: React.FC = () => {
  const [range, setRange] = useState<DateRange | undefined>()

  return (
    <div className='p-4'>
      <h2 className='text-lg font-bold mb-4'>Simple Range Test</h2>
      <DayPicker
        mode='range'
        selected={range}
        onSelect={(newRange) => {
          console.log('Simple range selected:', newRange)
          setRange(newRange)
        }}
        numberOfMonths={2}
      />
      <div className='mt-4 p-4 bg-gray-100 rounded'>
        <h3 className='font-semibold'>Selected Range:</h3>
        <p>From: {range?.from?.toLocaleDateString() || 'Not selected'}</p>
        <p>To: {range?.to?.toLocaleDateString() || 'Not selected'}</p>
      </div>
    </div>
  )
}
