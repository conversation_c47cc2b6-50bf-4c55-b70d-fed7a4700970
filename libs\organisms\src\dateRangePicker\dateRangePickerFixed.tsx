'use client'

import React, { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { CalendarDays } from 'lucide-react'
import { Button, Input } from '@social-media/atoms'
import {
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@social-media/molecules'
import { cn } from 'src/utils'
import { DateRangeType, SupportedDateDisplayFormat } from '../date-picker-common/types'

export interface DateRangePickerFixedProps {
  value?: DateRangeType
  onChange?: (range?: DateRangeType) => void
  label?: string
  error?: string
  required?: boolean
  className?: string
  inputClassName?: string
  disabled?: boolean
  placeholder?: string
  numberOfMonths?: 1 | 2
  locale?: any
  dateFormat?: SupportedDateDisplayFormat
  minDate?: Date
  maxDate?: Date
  useYearNavigation?: boolean
}

export const DateRangePickerFixed: React.FC<DateRangePickerFixedProps> = ({
  value,
  onChange,
  label,
  error,
  required,
  className,
  inputClassName,
  disabled,
  placeholder,
  numberOfMonths = 2,
  locale = vi,
  dateFormat = 'dd/MM/yyyy',
  minDate,
  maxDate,
  useYearNavigation = false,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [month, setMonth] = useState<Date>(value?.from || new Date())

  // Validate numberOfMonths - only 1 or 2 are allowed
  const validatedNumberOfMonths = numberOfMonths === 1 || numberOfMonths === 2 ? numberOfMonths : 2

  // Format the date range for display
  const formatDateRange = (range: DateRangeType | undefined): string => {
    if (!range || (!range.from && !range.to)) return ''

    try {
      if (range.from && !range.to) {
        return format(range.from, dateFormat, { locale })
      }
      if (range.from && range.to) {
        return `${format(range.from, dateFormat, { locale })} - ${format(
          range.to,
          dateFormat,
          { locale },
        )}`
      }
      return ''
    } catch (error) {
      console.error('Date formatting error:', error)
      return ''
    }
  }

  const formattedValue = formatDateRange(value)

  // Handle date range selection - simplified approach
  const handleSelect = (selectedRange: any) => {
    console.log('handleSelect called with:', selectedRange)
    
    if (!selectedRange) {
      onChange?.(undefined)
      return
    }

    // Create new range object
    const newRange: DateRangeType = {
      from: selectedRange.from,
      to: selectedRange.to,
    }

    console.log('Calling onChange with:', newRange)
    onChange?.(newRange)

    // Close popover when both dates are selected
    if (selectedRange.from && selectedRange.to) {
      setTimeout(() => setIsOpen(false), 100)
    }
  }

  // Update month when value changes
  useEffect(() => {
    if (value?.from) {
      setMonth(value.from)
    }
  }, [value?.from])

  return (
    <div className={cn('w-full space-y-1', className)}>
      {label && (
        <label
          htmlFor={label}
          className='block text-sm font-medium text-text-primary'
        >
          {label} {required && <span className='text-error'>*</span>}
        </label>
      )}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className='relative'>
            <Input
              id={label}
              type='text'
              value={formattedValue}
              readOnly
              onClick={() => !disabled && setIsOpen(true)}
              placeholder={placeholder || 'Chọn khoảng thời gian'}
              disabled={disabled}
              className={cn(
                'w-full pr-10 cursor-pointer min-w-[280px]',
                inputClassName,
                error && 'border-error focus:border-error focus:ring-error',
              )}
            />
            <Button
              type='button'
              variant='outline'
              size='icon'
              className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-1 border-0 hover:bg-gray-100 text-text-tertiary'
              onClick={() => !disabled && setIsOpen(!isOpen)}
              disabled={disabled}
              aria-label='Mở lịch'
            >
              <CalendarDays className='h-5 w-5' />
            </Button>
          </div>
        </PopoverTrigger>
        {!disabled && (
          <PopoverContent
            className={cn(
              'w-auto p-0',
              validatedNumberOfMonths === 2 && 'min-w-[540px]',
            )}
            align='start'
          >
            <div className='p-3'>
              <Calendar
                mode='range'
                defaultMonth={month}
                selected={value}
                onSelect={handleSelect}
                numberOfMonths={validatedNumberOfMonths}
                locale={locale}
                disabled={(date) => {
                  if (minDate && date < minDate) return true
                  if (maxDate && date > maxDate) return true
                  
                  // If we have a start date but no end date, disable dates before start date
                  if (value?.from && !value?.to && date < value.from) {
                    return true
                  }
                  
                  return false
                }}
                initialFocus
              />
            </div>
          </PopoverContent>
        )}
      </Popover>
      {error && <p className='mt-1 text-xs text-error'>{error}</p>}
    </div>
  )
}

export default DateRangePickerFixed
