import React from 'react'
import {
  useDeviceDetection,
  useDeviceType,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useMediaQuery,
  useResponsive,
  useBreakpoint,
  useHasTouch,
  useIsStandalone,
  usePrefersDarkMode,
} from './index'

/**
 * Demo component để test tất cả hooks
 * Có thể import vào Storybook hoặc test page
 */
export const DeviceDetectionDemo: React.FC = () => {
  // Device detection hooks
  const deviceInfo = useDeviceDetection()
  const deviceType = useDeviceType()
  const isMobile = useIsMobile()
  const isTablet = useIsTablet()
  const isDesktop = useIsDesktop()

  // Media query hooks
  const isMobileScreen = useMediaQuery('(max-width: 767px)')
  const responsive = useResponsive()
  const breakpoint = useBreakpoint()

  // Feature detection
  const hasTouch = useHasTouch()
  const isStandalone = useIsStandalone()
  const prefersDarkMode = usePrefersDarkMode()

  // Custom breakpoints example
  const customDevice = useDeviceDetection({
    mobile: 640,
    tablet: 1280,
    desktop: 1280,
  })

  return (
    <div className="p-6 space-y-6 bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      <h1 className="text-3xl font-bold mb-6">Device Detection Demo</h1>

      {/* Device Info */}
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Device Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Device Type:</strong> {deviceInfo.deviceType}
          </div>
          <div>
            <strong>Screen Size:</strong> {deviceInfo.screenWidth} x {deviceInfo.screenHeight}
          </div>
          <div>
            <strong>Orientation:</strong> {deviceInfo.orientation}
          </div>
          <div>
            <strong>Has Touch:</strong> {deviceInfo.hasTouch ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Standalone:</strong> {deviceInfo.isStandalone ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>User Agent:</strong> {deviceInfo.userAgent.substring(0, 50)}...
          </div>
        </div>
      </div>

      {/* Boolean Hooks */}
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Boolean Hooks</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className={`p-2 rounded ${isMobile ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>isMobile:</strong> {isMobile ? 'True' : 'False'}
          </div>
          <div className={`p-2 rounded ${isTablet ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>isTablet:</strong> {isTablet ? 'True' : 'False'}
          </div>
          <div className={`p-2 rounded ${isDesktop ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>isDesktop:</strong> {isDesktop ? 'True' : 'False'}
          </div>
          <div className={`p-2 rounded ${hasTouch ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>hasTouch:</strong> {hasTouch ? 'True' : 'False'}
          </div>
        </div>
      </div>

      {/* Media Query Hooks */}
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Media Query Hooks</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Mobile Screen:</strong> {isMobileScreen ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Current Breakpoint:</strong> {breakpoint}
          </div>
          <div>
            <strong>Can Hover:</strong> {responsive.canHover ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Prefers Dark Mode:</strong> {prefersDarkMode ? 'Yes' : 'No'}
          </div>
        </div>
      </div>

      {/* Custom Breakpoints */}
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Custom Breakpoints (640/1280)</h2>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className={`p-2 rounded ${customDevice.isMobile ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>Mobile:</strong> {customDevice.isMobile ? 'True' : 'False'}
          </div>
          <div className={`p-2 rounded ${customDevice.isTablet ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>Tablet:</strong> {customDevice.isTablet ? 'True' : 'False'}
          </div>
          <div className={`p-2 rounded ${customDevice.isDesktop ? 'bg-green-200 dark:bg-green-800' : 'bg-red-200 dark:bg-red-800'}`}>
            <strong>Desktop:</strong> {customDevice.isDesktop ? 'True' : 'False'}
          </div>
        </div>
      </div>

      {/* Responsive Component Example */}
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Responsive Component Example</h2>
        <div className="space-y-2">
          {isMobile && (
            <div className="bg-blue-200 dark:bg-blue-800 p-3 rounded">
              📱 Mobile View: Simplified layout
            </div>
          )}
          {isTablet && (
            <div className="bg-green-200 dark:bg-green-800 p-3 rounded">
              📱 Tablet View: Medium layout
            </div>
          )}
          {isDesktop && (
            <div className="bg-purple-200 dark:bg-purple-800 p-3 rounded">
              🖥️ Desktop View: Full layout
            </div>
          )}
        </div>
      </div>

      {/* Real-time Updates */}
      <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">🔄 Real-time Updates</h2>
        <p className="text-sm">
          Resize your browser window or rotate your device to see the hooks update in real-time!
        </p>
      </div>
    </div>
  )
}

export default DeviceDetectionDemo
