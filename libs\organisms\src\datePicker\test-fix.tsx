import React, { useState } from 'react'
import { DatePicker } from './datePicker'

export const TestDatePickerFix: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)

  return (
    <div className='p-8 space-y-4'>
      <h2 className='text-xl font-bold'>Test DatePicker Fix</h2>
      
      <DatePicker
        label='Test Date Selection'
        value={selectedDate}
        onChange={(date) => {
          console.log('Date changed:', date)
          setSelectedDate(date as Date)
        }}
        dateFormat='dd/MM/yyyy'
      />
      
      <div className='p-4 bg-gray-100 rounded'>
        <h3 className='font-semibold'>Debug Info:</h3>
        <p>Selected Date: {selectedDate ? selectedDate.toLocaleDateString('vi-VN') : 'None'}</p>
        <p>Selected Date ISO: {selectedDate ? selectedDate.toISOString() : 'None'}</p>
      </div>
      
      <div className='space-x-2'>
        <button
          onClick={() => setSelectedDate(new Date())}
          className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
        >
          Set Today
        </button>
        <button
          onClick={() => setSelectedDate(undefined)}
          className='px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600'
        >
          Clear
        </button>
      </div>
    </div>
  )
}
