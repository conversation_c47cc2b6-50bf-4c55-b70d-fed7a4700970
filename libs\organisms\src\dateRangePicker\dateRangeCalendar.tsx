// File: libs/organisms/src/dateRangePicker/dateRangeCalendar.tsx
import { Locale } from 'date-fns'
import React, { useMemo, useState } from 'react'
import { CalendarGrid } from '../date-picker-common/CalendarGrid'
import { CalendarHeader } from '../date-picker-common/CalendarHeader'
import { DateRangeType } from '../date-picker-common/types'
import {
  generateMonthDays,
  getMonthName,
  getYearsRange,
} from '../date-picker-common/utils'

interface DateRangeCalendarProps {
  value?: DateRangeType
  onSelect: (range: DateRangeType | undefined) => void
  numberOfMonths: 1 | 2
  locale: Locale
  minDate?: Date
  maxDate?: Date
  disabled?: (date: Date) => boolean
}

export const DateRangeCalendar: React.FC<DateRangeCalendarProps> = ({
  value,
  onSelect,
  numberOfMonths,
  locale,
  minDate,
  maxDate,
  disabled,
}) => {
  // Simple state management - no conflicts
  const [firstCalendarDate, setFirstCalendarDate] = useState(() => {
    if (value?.from)
      return new Date(value.from.getFullYear(), value.from.getMonth(), 1)
    return new Date()
  })

  const [secondCalendarDate, setSecondCalendarDate] = useState(() => {
    if (value?.from) {
      return new Date(value.from.getFullYear(), value.from.getMonth() + 1, 1)
    }
    const nextMonth = new Date()
    nextMonth.setMonth(nextMonth.getMonth() + 1)
    return nextMonth
  })

  // Generate years and months data
  const years = useMemo(() => getYearsRange(new Date().getFullYear()), [])
  const months = useMemo(
    () =>
      Array.from({ length: 12 }, (_, i) => ({
        value: i,
        label: getMonthName(i, locale),
      })),
    [locale],
  )

  // Navigation handlers for first calendar
  const handleFirstPrevMonth = () => {
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(newDate.getMonth() - 1)
    setFirstCalendarDate(newDate)
  }

  const handleFirstNextMonth = () => {
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(newDate.getMonth() + 1)
    setFirstCalendarDate(newDate)
  }

  const handleFirstYearChange = (year: number) => {
    const newDate = new Date(firstCalendarDate)
    newDate.setFullYear(year)
    setFirstCalendarDate(newDate)
  }

  const handleFirstMonthChange = (month: number) => {
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(month)
    setFirstCalendarDate(newDate)
  }

  // Navigation handlers for second calendar
  const handleSecondPrevMonth = () => {
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(newDate.getMonth() - 1)
    setSecondCalendarDate(newDate)
  }

  const handleSecondNextMonth = () => {
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(newDate.getMonth() + 1)
    setSecondCalendarDate(newDate)
  }

  const handleSecondYearChange = (year: number) => {
    const newDate = new Date(secondCalendarDate)
    newDate.setFullYear(year)
    setSecondCalendarDate(newDate)
  }

  const handleSecondMonthChange = (month: number) => {
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(month)
    setSecondCalendarDate(newDate)
  }

  // Date selection handler
  const handleDateSelect = (date: Date) => {
    if (!value?.from || (value.from && value.to)) {
      // Start new selection
      onSelect({ from: date, to: undefined })
    } else if (value.from && !value.to) {
      // Complete the range
      if (date >= value.from) {
        onSelect({ from: value.from, to: date })
      } else {
        onSelect({ from: date, to: value.from })
      }
    }
  }

  // Optimized range helper functions with memoization
  const rangeHelpers = useMemo(() => {
    const isDateInRange = (date: Date): boolean => {
      if (!value?.from || !value?.to) return false
      return date >= value.from && date <= value.to
    }

    const isDateRangeStart = (date: Date): boolean => {
      if (!value?.from) return false
      return date.getTime() === value.from.getTime()
    }

    const isDateRangeEnd = (date: Date): boolean => {
      if (!value?.to) return false
      return date.getTime() === value.to.getTime()
    }

    const isDateSelected = (date: Date): boolean => {
      return isDateRangeStart(date) || isDateRangeEnd(date)
    }

    return { isDateInRange, isDateRangeStart, isDateRangeEnd, isDateSelected }
  }, [value?.from, value?.to])

  // Generate calendar data
  const firstMonthData = useMemo(() => {
    const days = generateMonthDays(
      firstCalendarDate,
      undefined,
      minDate,
      maxDate,
      true,
    )
    return {
      days: days.map((day) => ({
        ...day,
        isSelected: rangeHelpers.isDateSelected(day.date),
        isInRange: rangeHelpers.isDateInRange(day.date),
        isRangeStart: rangeHelpers.isDateRangeStart(day.date),
        isRangeEnd: rangeHelpers.isDateRangeEnd(day.date),
      })),
      monthName: getMonthName(firstCalendarDate.getMonth(), locale),
      year: firstCalendarDate.getFullYear(),
    }
  }, [firstCalendarDate, rangeHelpers, minDate, maxDate, locale])

  const secondMonthData = useMemo(() => {
    const days = generateMonthDays(
      secondCalendarDate,
      undefined,
      minDate,
      maxDate,
      true,
    )
    return {
      days: days.map((day) => ({
        ...day,
        isSelected: rangeHelpers.isDateSelected(day.date),
        isInRange: rangeHelpers.isDateInRange(day.date),
        isRangeStart: rangeHelpers.isDateRangeStart(day.date),
        isRangeEnd: rangeHelpers.isDateRangeEnd(day.date),
      })),
      monthName: getMonthName(secondCalendarDate.getMonth(), locale),
      year: secondCalendarDate.getFullYear(),
    }
  }, [secondCalendarDate, rangeHelpers, minDate, maxDate, locale])

  if (numberOfMonths === 1) {
    return (
      <div className='space-y-3'>
        <CalendarHeader
          monthName={firstMonthData.monthName}
          year={firstMonthData.year}
          currentMonth={firstCalendarDate.getMonth()}
          currentYear={firstCalendarDate.getFullYear()}
          years={years}
          months={months}
          prevMonth={handleFirstPrevMonth}
          nextMonth={handleFirstNextMonth}
          setYear={handleFirstYearChange}
          setMonth={handleFirstMonthChange}
          useYearNavigation={true}
        />
        <CalendarGrid
          days={firstMonthData.days}
          onDateSelect={handleDateSelect}
          locale={locale}
        />
      </div>
    )
  }

  return (
    <div className='space-y-3'>
      <div className='grid grid-cols-2 gap-4'>
        {/* First Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={firstMonthData.monthName}
            year={firstMonthData.year}
            currentMonth={firstCalendarDate.getMonth()}
            currentYear={firstCalendarDate.getFullYear()}
            years={years}
            months={months}
            prevMonth={handleFirstPrevMonth}
            nextMonth={handleFirstNextMonth}
            setYear={handleFirstYearChange}
            setMonth={handleFirstMonthChange}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={firstMonthData.days}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>

        {/* Second Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={secondMonthData.monthName}
            year={secondMonthData.year}
            currentMonth={secondCalendarDate.getMonth()}
            currentYear={secondCalendarDate.getFullYear()}
            years={years}
            months={months}
            prevMonth={handleSecondPrevMonth}
            nextMonth={handleSecondNextMonth}
            setYear={handleSecondYearChange}
            setMonth={handleSecondMonthChange}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={secondMonthData.days}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>
      </div>
    </div>
  )
}
