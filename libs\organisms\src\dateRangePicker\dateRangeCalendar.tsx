// File: libs/organisms/src/dateRangePicker/dateRangeCalendar.tsx
import { Locale } from 'date-fns'
import React from 'react'
import { CalendarGrid } from '../date-picker-common/CalendarGrid'
import { CalendarHeader } from '../date-picker-common/CalendarHeader'
import { DateRangeType } from '../date-picker-common/types'
import { useDatePickerBaseLogic } from '../date-picker-common/useDatePickerBaseLogic'

interface DateRangeCalendarProps {
  value?: DateRangeType
  onSelect: (range: DateRangeType | undefined) => void
  numberOfMonths: 1 | 2
  locale: Locale
  minDate?: Date
  maxDate?: Date
  disabled?: (date: Date) => boolean
}

export const DateRangeCalendar: React.FC<DateRangeCalendarProps> = ({
  value,
  onSelect,
  numberOfMonths,
  locale,
  minDate,
  maxDate,
  disabled,
}) => {
  // Use base logic for the calendar
  const baseLogic = useDatePickerBaseLogic({
    value: value?.from,
    minDate,
    maxDate,
    locale,
    useYearNavigation: true,
    numberOfMonths,
    isRangePicker: true,
    initialVisibleDate: value?.from || new Date(),
  })

  const handleDateSelect = (date: Date) => {
    if (!value?.from || (value.from && value.to)) {
      // Start new selection
      onSelect({ from: date, to: undefined })
    } else if (value.from && !value.to) {
      // Complete the range
      if (date >= value.from) {
        onSelect({ from: value.from, to: date })
      } else {
        onSelect({ from: date, to: value.from })
      }
    }
  }

  const isDateInRange = (date: Date): boolean => {
    if (!value?.from || !value?.to) return false
    return date >= value.from && date <= value.to
  }

  const isDateRangeStart = (date: Date): boolean => {
    if (!value?.from) return false
    return date.getTime() === value.from.getTime()
  }

  const isDateRangeEnd = (date: Date): boolean => {
    if (!value?.to) return false
    return date.getTime() === value.to.getTime()
  }

  const isDateSelected = (date: Date): boolean => {
    return isDateRangeStart(date) || isDateRangeEnd(date)
  }

  // Enhance days with range information
  const enhanceDaysWithRange = (days: any[]) => {
    return days.map((day) => ({
      ...day,
      isSelected: isDateSelected(day.date),
      isInRange: isDateInRange(day.date),
      isRangeStart: isDateRangeStart(day.date),
      isRangeEnd: isDateRangeEnd(day.date),
    }))
  }

  if (numberOfMonths === 1) {
    const monthData = baseLogic.visibleMonthsData[0]
    const enhancedDays = enhanceDaysWithRange(monthData.days)

    return (
      <div className='space-y-3'>
        <CalendarHeader
          monthName={monthData.monthName}
          year={monthData.year}
          currentMonth={baseLogic.currentMonth}
          currentYear={baseLogic.currentYear}
          years={baseLogic.years}
          months={baseLogic.months}
          prevMonth={baseLogic.prevMonth}
          nextMonth={baseLogic.nextMonth}
          setYear={baseLogic.setYear}
          setMonth={baseLogic.setMonth}
          useYearNavigation={true}
        />
        <CalendarGrid
          days={enhancedDays}
          onDateSelect={handleDateSelect}
          locale={locale}
        />
      </div>
    )
  }

  // For 2 months
  const firstMonth = baseLogic.visibleMonthsData[0]
  const secondMonth = baseLogic.visibleMonthsData[1]

  const firstMonthDays = enhanceDaysWithRange(firstMonth.days)
  const secondMonthDays = enhanceDaysWithRange(secondMonth.days)

  return (
    <div className='space-y-3'>
      <div className='grid grid-cols-2 gap-4'>
        {/* First Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={firstMonth.monthName}
            year={firstMonth.year}
            currentMonth={baseLogic.currentMonth}
            currentYear={baseLogic.currentYear}
            years={baseLogic.years}
            months={baseLogic.months}
            prevMonth={baseLogic.prevMonth}
            nextMonth={baseLogic.nextMonth}
            setYear={baseLogic.setYear}
            setMonth={baseLogic.setMonth}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={firstMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>

        {/* Second Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={secondMonth.monthName}
            year={secondMonth.year}
            currentMonth={baseLogic.currentMonth + 1}
            currentYear={secondMonth.year}
            years={baseLogic.years}
            months={baseLogic.months}
            prevMonth={baseLogic.prevMonth}
            nextMonth={baseLogic.nextMonth}
            setYear={(year) => {
              // Set year for second month
              const newDate = new Date(baseLogic.currentDisplayedMonthDate)
              newDate.setFullYear(year)
              newDate.setMonth(newDate.getMonth() + 1)
              baseLogic.setCurrentDisplayedMonthDate(newDate)
            }}
            setMonth={(month) => {
              // Set month for second month
              const newDate = new Date(baseLogic.currentDisplayedMonthDate)
              newDate.setMonth(month - 1) // -1 because second month is +1 from first
              baseLogic.setCurrentDisplayedMonthDate(newDate)
            }}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={secondMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>
      </div>
    </div>
  )
}
