{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "rootDir": "../..", "baseUrl": ".", "skipLibCheck": true, "allowJs": true, "paths": {"@social-media/assets": ["../../assets/index.ts"], "@assets/*": ["../../assets/*"], "@social-media/hooks": ["../../hooks/index.ts"], "@hooks/*": ["../../hooks/*"], "src/*": ["src/*"]}, "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "jsx": "react-jsx", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo"}, "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs", "**/*.stories.ts", "**/*.stories.js", "**/*.stories.jsx", "**/*.stories.tsx"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]}