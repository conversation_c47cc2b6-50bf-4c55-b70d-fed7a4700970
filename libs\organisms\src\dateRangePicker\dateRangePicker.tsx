// File: libs/organisms/src/dateRangePicker/dateRangePicker.tsx
import { Button, Input } from '@social-media/atoms'
import {
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@social-media/molecules'
import { CalendarDays } from 'lucide-react'
import React from 'react'
import { DateRange } from 'react-day-picker'
import { cn } from 'src/utils'
import {
  DateRangeType,
  SupportedDateDisplayFormat,
} from '../date-picker-common/types'
import { MonthYearSelector } from './monthYearSelector'
import { useDateRangePicker } from './useDateRangePicker'
import './dateRangePicker.module.css'

export interface DateRangePickerProps {
  value?: DateRangeType
  onChange?: (range?: DateRangeType) => void
  label?: string
  error?: string
  required?: boolean
  className?: string
  inputClassName?: string
  disabled?: boolean
  placeholder?: string
  numberOfMonths?: 1 | 2
  locale?: any
  dateFormat?: SupportedDateDisplayFormat
  minDate?: Date
  maxDate?: Date
  useYearNavigation?: boolean
}

export const DateRangePicker: React.FC<DateRangePickerProps> = (props) => {
  const {
    label,
    error,
    required,
    className,
    inputClassName,
    disabled,
    numberOfMonths = 2,
    useYearNavigation = false,
    minDate,
    maxDate,
    placeholder,
    onChange,
    value,
    dateFormat = 'DD/MM/YYYY',
  } = props

  const {
    isOpen,
    setIsOpen,
    formattedValue,
    handleSelect,
    month,
    handleMonthChange,
    currentYear,
    currentMonth,
    handleYearChange,
    handleMonthSelectChange,
    locale,
  } = useDateRangePicker(props)

  // Convert DateRangeType to DateRange for react-day-picker
  const dayPickerRange: DateRange | undefined = props.value
    ? {
        from: props.value.from,
        to: props.value.to,
      }
    : undefined

  return (
    <div className={cn('w-full space-y-1', className)}>
      {label && (
        <label
          htmlFor={label}
          className='block text-sm font-medium text-text-primary'
        >
          {label} {required && <span className='text-error'>*</span>}
        </label>
      )}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className='relative'>
            <Input
              id={label}
              type='text'
              value={formattedValue}
              readOnly
              onClick={() => !disabled && setIsOpen(true)}
              placeholder={placeholder || 'Chọn khoảng thời gian'}
              disabled={disabled}
              className={cn(
                'w-full pr-10 cursor-pointer',
                inputClassName,
                error && 'border-error focus:border-error focus:ring-error',
              )}
            />
            <Button
              type='button'
              variant='outline'
              size='icon'
              className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-1 border-0 hover:bg-gray-100 text-text-tertiary'
              onClick={() => !disabled && setIsOpen(!isOpen)}
              disabled={disabled}
              aria-label='Mở lịch'
            >
              <CalendarDays className='h-5 w-5' />
            </Button>
          </div>
        </PopoverTrigger>
        {!disabled && (
          <PopoverContent
            className={cn(
              'w-auto p-0',
              numberOfMonths === 2 && 'min-w-[540px]',
            )}
            align='start'
          >
            <div className='p-3'>
              {useYearNavigation && (
                <MonthYearSelector
                  currentMonth={currentMonth}
                  currentYear={currentYear}
                  onYearChange={handleYearChange}
                  onMonthChange={handleMonthSelectChange}
                  locale={locale}
                />
              )}
              <Calendar
                mode='range'
                defaultMonth={month}
                month={month}
                onMonthChange={handleMonthChange}
                selected={dayPickerRange}
                onSelect={handleSelect}
                numberOfMonths={numberOfMonths}
                locale={locale}
                disabled={(date) => {
                  if (minDate && date < minDate) return true
                  if (maxDate && date > maxDate) return true
                  return false
                }}
                initialFocus
                classNames={{
                  months:
                    numberOfMonths === 2
                      ? 'flex flex-row space-y-0 space-x-4'
                      : 'flex flex-col space-y-4',
                  month: 'space-y-4',
                  caption: cn(
                    'flex justify-center pt-1 relative items-center',
                    useYearNavigation && 'hidden',
                  ),
                  caption_label: 'text-sm font-medium',
                  nav: 'space-x-1 flex items-center',
                  nav_button: cn(
                    'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',
                    useYearNavigation && 'hidden',
                  ),
                  nav_button_previous: 'absolute left-1',
                  nav_button_next: 'absolute right-1',
                  table: 'w-full border-collapse space-y-1',
                  head_row: 'flex',
                  head_cell:
                    'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',
                  row: 'flex w-full mt-2',
                  cell: 'text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20 h-9 w-9',
                  day: cn(
                    'h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground',
                  ),
                  day_range_start: 'day-range-start',
                  day_range_end: 'day-range-end',
                  day_selected:
                    'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground rounded-none',
                  day_today: 'bg-accent text-accent-foreground',
                  day_outside:
                    'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',
                  day_disabled: 'text-muted-foreground opacity-50',
                  day_range_middle:
                    'aria-selected:bg-primary/10 aria-selected:text-accent-foreground rounded-none',
                  day_hidden: 'invisible',
                }}
                modifiersClassNames={{
                  range_start: 'rounded-l-md rounded-r-none',
                  range_end: 'rounded-r-md rounded-l-none',
                  range_middle: 'rounded-none',
                }}
              />
            </div>
          </PopoverContent>
        )}
      </Popover>
      {error && <p className='mt-1 text-xs text-error'>{error}</p>}
    </div>
  )
}

export default DateRangePicker
