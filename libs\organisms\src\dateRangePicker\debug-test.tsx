import React, { useState } from 'react'
import { DateRange, DayPicker } from 'react-day-picker'
import { DateRangeType } from '../date-picker-common/types'
import { DateRangePicker } from './dateRangePicker'
import { DateRangePickerFixed } from './dateRangePickerFixed'

// Test component để debug DateRangePicker
export const DebugDateRangePicker: React.FC = () => {
  const [range, setRange] = useState<DateRangeType | undefined>()

  console.log('Current range state:', range)

  return (
    <div className='p-8 space-y-8'>
      <div>
        <h2 className='text-xl font-bold mb-4'>Debug DateRangePicker</h2>

        <DateRangePicker
          label='Test DateRangePicker'
          value={range}
          onChange={(newRange) => {
            console.log('onChange called with:', newRange)
            setRange(newRange)
          }}
          placeholder='Chọn khoảng ngày'
          dateFormat='dd/MM/yyyy'
          numberOfMonths={2}
        />

        <div className='mt-4 p-4 bg-gray-100 rounded'>
          <h3 className='font-semibold'>Debug Info:</h3>
          <p>From: {range?.from ? range.from.toISOString() : 'undefined'}</p>
          <p>To: {range?.to ? range.to.toISOString() : 'undefined'}</p>
          <p>JSON: {JSON.stringify(range, null, 2)}</p>
        </div>
      </div>

      <div>
        <h2 className='text-xl font-bold mb-4'>Fixed DateRangePicker</h2>
        <FixedDateRangePickerTest />
      </div>

      <div>
        <h2 className='text-xl font-bold mb-4'>Raw DayPicker Test</h2>
        <RawDayPickerTest />
      </div>
    </div>
  )
}

// Test với DateRangePickerFixed
const FixedDateRangePickerTest: React.FC = () => {
  const [range, setRange] = useState<DateRangeType | undefined>()

  console.log('Fixed DateRangePicker range:', range)

  return (
    <div>
      <DateRangePickerFixed
        label='Test Fixed DateRangePicker'
        value={range}
        onChange={(newRange: DateRangeType | undefined) => {
          console.log('Fixed DateRangePicker onChange:', newRange)
          setRange(newRange)
        }}
        placeholder='Chọn khoảng ngày'
        dateFormat='dd/MM/yyyy'
        numberOfMonths={2}
      />

      <div className='mt-4 p-4 bg-green-100 rounded'>
        <h3 className='font-semibold'>Fixed DateRangePicker Debug:</h3>
        <p>From: {range?.from ? range.from.toISOString() : 'undefined'}</p>
        <p>To: {range?.to ? range.to.toISOString() : 'undefined'}</p>
        <p>JSON: {JSON.stringify(range, null, 2)}</p>
      </div>
    </div>
  )
}

// Test với DayPicker trực tiếp
const RawDayPickerTest: React.FC = () => {
  const [range, setRange] = useState<DateRange | undefined>()

  console.log('Raw DayPicker range:', range)

  return (
    <div>
      <DayPicker
        mode='range'
        selected={range}
        onSelect={(newRange) => {
          console.log('Raw DayPicker onSelect:', newRange)
          setRange(newRange)
        }}
        numberOfMonths={2}
      />

      <div className='mt-4 p-4 bg-blue-100 rounded'>
        <h3 className='font-semibold'>Raw DayPicker Debug:</h3>
        <p>From: {range?.from ? range.from.toISOString() : 'undefined'}</p>
        <p>To: {range?.to ? range.to.toISOString() : 'undefined'}</p>
        <p>JSON: {JSON.stringify(range, null, 2)}</p>
      </div>
    </div>
  )
}
