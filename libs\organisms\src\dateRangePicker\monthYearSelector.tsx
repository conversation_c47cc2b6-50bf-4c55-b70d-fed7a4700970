// File: libs/organisms/src/dateRangePicker/MonthYearSelector.tsx
import {
  Button,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@social-media/atoms'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import React, { useMemo } from 'react'

interface MonthYearSelectorProps {
  currentMonth: number
  currentYear: number
  onYearChange: (year: number) => void
  onMonthChange: (month: number) => void
  locale?: any
  minYear?: number
  maxYear?: number
}

export const MonthYearSelector: React.FC<MonthYearSelectorProps> = ({
  currentMonth,
  currentYear,
  onYearChange,
  onMonthChange,
  locale,
  minYear = 1900,
  maxYear = 2100,
}) => {
  // Generate month names based on locale
  const monthNames = useMemo(() => {
    const months = []
    for (let i = 0; i < 12; i++) {
      const date = new Date(2000, i, 1)
      const monthName = date.toLocaleDateString(locale?.code || 'vi-VN', {
        month: 'long',
      })
      months.push({
        value: i,
        label: monthName.charAt(0).toUpperCase() + monthName.slice(1),
      })
    }
    return months
  }, [locale])

  // Generate years array
  const years = useMemo(() => {
    const yearsArray = []
    for (let year = maxYear; year >= minYear; year--) {
      yearsArray.push(year)
    }
    return yearsArray
  }, [minYear, maxYear])

  const handlePrevMonth = () => {
    if (currentMonth === 0) {
      onMonthChange(11)
      onYearChange(currentYear - 1)
    } else {
      onMonthChange(currentMonth - 1)
    }
  }

  const handleNextMonth = () => {
    if (currentMonth === 11) {
      onMonthChange(0)
      onYearChange(currentYear + 1)
    } else {
      onMonthChange(currentMonth + 1)
    }
  }

  return (
    <div className='flex items-center justify-between px-2 pb-3 border-b'>
      <Button
        variant='outline'
        size='icon'
        className='h-7 w-7'
        onClick={handlePrevMonth}
        type='button'
      >
        <ChevronLeft className='h-4 w-4' />
      </Button>

      <div className='flex items-center space-x-2 flex-1 justify-center'>
        <Select
          value={String(currentMonth)}
          onValueChange={(value) => onMonthChange(parseInt(value, 10))}
        >
          <SelectTrigger className='h-9 text-sm px-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary bg-white min-w-[100px]'>
            <SelectValue>
              {monthNames.find((m) => m.value === currentMonth)?.label ?? 'Tháng'}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className='max-h-60 bg-white shadow-lg rounded-md border border-gray-200'>
            {monthNames.map((month) => (
              <SelectItem key={month.value} value={String(month.value)}>
                {month.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={String(currentYear)}
          onValueChange={(value) => onYearChange(parseInt(value, 10))}
        >
          <SelectTrigger className='h-9 text-sm px-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary bg-white min-w-[80px]'>
            <SelectValue>{currentYear}</SelectValue>
          </SelectTrigger>
          <SelectContent className='max-h-60 bg-white shadow-lg rounded-md border border-gray-200'>
            {years.map((year) => (
              <SelectItem key={year} value={String(year)}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Button
        variant='outline'
        size='icon'
        className='h-7 w-7'
        onClick={handleNextMonth}
        type='button'
      >
        <ChevronRight className='h-4 w-4' />
      </Button>
    </div>
  )
}
