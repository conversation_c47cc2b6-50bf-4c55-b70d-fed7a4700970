// File: libs/organisms/src/dateRangePicker/dateRangePicker.tsx
import { Button, Input } from '@social-media/atoms'
import {
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@social-media/molecules'
import { CalendarDays } from 'lucide-react'
import React from 'react'
import { DateRange } from 'react-day-picker'
import { cn } from 'src/utils'
import {
  DateRangeType,
  SupportedDateDisplayFormat,
} from '../date-picker-common/types'
import './dateRangePicker.module.css'
import { MonthYearSelector } from './monthYearSelector'
import { useDateRangePicker } from './useDateRangePicker'

export interface DateRangePickerProps {
  value?: DateRangeType
  onChange?: (range?: DateRangeType) => void
  label?: string
  error?: string
  required?: boolean
  className?: string
  inputClassName?: string
  disabled?: boolean
  placeholder?: string
  numberOfMonths?: 1 | 2
  locale?: any
  dateFormat?: SupportedDateDisplayFormat
  minDate?: Date
  maxDate?: Date
  useYearNavigation?: boolean
}

export const DateRangePicker: React.FC<DateRangePickerProps> = (props) => {
  const {
    label,
    error,
    required,
    className,
    inputClassName,
    disabled,
    numberOfMonths = 2,
    useYearNavigation = false,
    minDate,
    maxDate,
    placeholder,
    onChange,
    value,
    dateFormat = 'dd/MM/yyyy',
  } = props

  // Validate numberOfMonths - only 1 or 2 are allowed
  const validatedNumberOfMonths =
    numberOfMonths === 1 || numberOfMonths === 2 ? numberOfMonths : 2

  const {
    isOpen,
    setIsOpen,
    formattedValue,
    handleSelect,
    month,
    handleMonthChange,
    currentYear,
    currentMonth,
    handleYearChange,
    handleMonthSelectChange,
    handleDateHover,
    getPreviewRange,
    locale,
  } = useDateRangePicker(props)

  // Convert DateRangeType to DateRange for react-day-picker
  const dayPickerRange: DateRange | undefined = props.value
    ? {
        from: props.value.from,
        to: props.value.to,
      }
    : undefined

  // Get preview range for hover effect
  const previewRange = getPreviewRange()
  const displayRange = previewRange || dayPickerRange

  return (
    <div className={cn('w-full space-y-1', className)}>
      {label && (
        <label
          htmlFor={label}
          className='block text-sm font-medium text-text-primary'
        >
          {label} {required && <span className='text-error'>*</span>}
        </label>
      )}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className='relative'>
            <Input
              id={label}
              type='text'
              value={formattedValue}
              readOnly
              onClick={() => !disabled && setIsOpen(true)}
              placeholder={placeholder || 'Chọn khoảng thời gian'}
              disabled={disabled}
              className={cn(
                'w-full pr-10 cursor-pointer min-w-[280px]',
                inputClassName,
                error && 'border-error focus:border-error focus:ring-error',
              )}
            />
            <Button
              type='button'
              variant='outline'
              size='icon'
              className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-1 border-0 hover:bg-gray-100 text-text-tertiary'
              onClick={() => !disabled && setIsOpen(!isOpen)}
              disabled={disabled}
              aria-label='Mở lịch'
            >
              <CalendarDays className='h-5 w-5' />
            </Button>
          </div>
        </PopoverTrigger>
        {!disabled && (
          <PopoverContent
            className={cn(
              'w-auto p-0',
              validatedNumberOfMonths === 2 && 'min-w-[540px]',
            )}
            align='start'
          >
            <div className='p-3'>
              {useYearNavigation && (
                <MonthYearSelector
                  currentMonth={currentMonth}
                  currentYear={currentYear}
                  onYearChange={handleYearChange}
                  onMonthChange={handleMonthSelectChange}
                  locale={locale}
                />
              )}
              <Calendar
                mode='range'
                defaultMonth={month}
                month={month}
                onMonthChange={handleMonthChange}
                selected={dayPickerRange}
                onSelect={handleSelect}
                numberOfMonths={validatedNumberOfMonths}
                locale={locale}
                disabled={(date) => {
                  if (minDate && date < minDate) return true
                  if (maxDate && date > maxDate) return true

                  // If we have a start date but no end date, disable dates before start date
                  if (
                    props.value?.from &&
                    !props.value?.to &&
                    date < props.value.from
                  ) {
                    return true
                  }

                  return false
                }}
                initialFocus
                className={cn(
                  useYearNavigation &&
                    '[&_.rdp-caption]:hidden [&_.rdp-nav]:hidden',
                )}
              />
            </div>
          </PopoverContent>
        )}
      </Popover>
      {error && <p className='mt-1 text-xs text-error'>{error}</p>}
    </div>
  )
}

export default DateRangePicker
