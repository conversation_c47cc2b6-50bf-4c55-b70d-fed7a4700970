import React, { useState } from 'react'
import { DayPicker, DateRange } from 'react-day-picker'
import { DateRangeType } from '../date-picker-common/types'

// Minimal test component để test DayPicker trực tiếp
export const MinimalTest: React.FC = () => {
  const [range, setRange] = useState<DateRange | undefined>()

  console.log('MinimalTest range:', range)

  return (
    <div className="p-8">
      <h2 className="text-xl font-bold mb-4">Minimal DayPicker Test</h2>
      
      <DayPicker
        mode="range"
        selected={range}
        onSelect={(newRange) => {
          console.log('MinimalTest onSelect:', newRange)
          setRange(newRange)
        }}
        numberOfMonths={2}
      />
      
      <div className="mt-4 p-4 bg-yellow-100 rounded">
        <h3 className="font-semibold">Minimal Test Debug:</h3>
        <p>From: {range?.from ? range.from.toLocaleDateString() : 'undefined'}</p>
        <p>To: {range?.to ? range.to.toLocaleDateString() : 'undefined'}</p>
        <p>JSON: {JSON.stringify(range, null, 2)}</p>
      </div>
      
      <div className="mt-4 p-4 bg-blue-100 rounded">
        <h3 className="font-semibold">Instructions:</h3>
        <ol className="text-sm space-y-1">
          <li>1. Click on a start date</li>
          <li>2. Click on an end date</li>
          <li>3. Check if both dates are captured correctly</li>
        </ol>
      </div>
    </div>
  )
}

// Test với Calendar wrapper
export const CalendarWrapperTest: React.FC = () => {
  const [range, setRange] = useState<DateRange | undefined>()

  console.log('CalendarWrapperTest range:', range)

  return (
    <div className="p-8">
      <h2 className="text-xl font-bold mb-4">Calendar Wrapper Test</h2>
      
      {/* Import Calendar component dynamically to avoid build issues */}
      <div className="border p-4 rounded">
        <p>Calendar wrapper test will be implemented here</p>
      </div>
      
      <div className="mt-4 p-4 bg-purple-100 rounded">
        <h3 className="font-semibold">Calendar Wrapper Debug:</h3>
        <p>From: {range?.from ? range.from.toLocaleDateString() : 'undefined'}</p>
        <p>To: {range?.to ? range.to.toLocaleDateString() : 'undefined'}</p>
        <p>JSON: {JSON.stringify(range, null, 2)}</p>
      </div>
    </div>
  )
}
