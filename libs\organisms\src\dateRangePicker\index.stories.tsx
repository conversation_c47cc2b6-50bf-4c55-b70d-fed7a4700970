// File: libs/organisms/src/dateRangePicker/index.stories.tsx
import { action } from '@storybook/addon-actions'
import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import type { DateRangeType } from '../date-picker-common/types'
import { SupportedDateDisplayFormat } from '../date-picker-common/types'
import { DateRangePicker } from './dateRangePicker'

const meta: Meta<typeof DateRangePicker> = {
  title: 'Organisms/DateRangePicker',
  component: DateRangePicker,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
DateRangePicker component cung cấp một giao diện trực quan để chọn khoảng ngày tháng. Component hiển thị hai tháng cạnh nhau để dễ dàng chọn ngày bắt đầu và kết thúc.

### Các định dạng ngày được hỗ trợ:
- \`PPP\`: Định dạng đầy đủ (ví dụ: "April 29th, 2023")
- \`dd/MM/yyyy\`: Định dạng Việt Nam (ví dụ: "29/04/2023")
- \`MM/dd/yyyy\`: Định dạng Mỹ (ví dụ: "04/29/2023")
- \`yyyy-MM-dd\`: Định dạng ISO (ví dụ: "2023-04-29")
- \`dd MMM yy\`: Định dạng ngắn (ví dụ: "29 Apr 23")
- \`d MMMM yyyy\`: Định dạng dài (ví dụ: "29 April 2023")

### Cách chọn ngày:
1. Click vào ngày đầu tiên để chọn ngày bắt đầu
2. Click vào ngày thứ hai để chọn ngày kết thúc
3. Hover trên các ngày để xem preview khoảng thời gian

### Accessibility:
- Hỗ trợ keyboard navigation
- ARIA labels đầy đủ
- Focus management tốt
- Screen reader friendly
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      description: 'Giá trị khoảng ngày được chọn (object với from và to)',
      control: { type: 'object' },
    },
    onChange: {
      description: 'Callback được gọi khi khoảng ngày được chọn thay đổi',
      action: 'onChange',
    },
    label: {
      description: 'Label hiển thị phía trên input',
      control: { type: 'text' },
    },
    placeholder: {
      description: 'Placeholder text cho input',
      control: { type: 'text' },
    },
    dateFormat: {
      description: 'Định dạng hiển thị ngày',
      control: { type: 'select' },
      options: [
        'PPP',
        'dd/MM/yyyy',
        'MM/dd/yyyy',
        'yyyy-MM-dd',
        'dd MMM yy',
        'd MMMM yyyy',
      ] as SupportedDateDisplayFormat[],
    },
    numberOfMonths: {
      description: 'Số tháng hiển thị cạnh nhau (chỉ được phép 1 hoặc 2)',
      control: { type: 'select' },
      options: [1, 2],
    },
    disabled: {
      description: 'Vô hiệu hóa component',
      control: { type: 'boolean' },
    },
    required: {
      description: 'Đánh dấu trường bắt buộc',
      control: { type: 'boolean' },
    },
    error: {
      description: 'Thông báo lỗi',
      control: { type: 'text' },
    },
    minDate: {
      description: 'Ngày tối thiểu có thể chọn',
      control: { type: 'date' },
    },
    maxDate: {
      description: 'Ngày tối đa có thể chọn',
      control: { type: 'date' },
    },
    useYearNavigation: {
      description: 'Hiển thị dropdown chọn năm/tháng thay vì chỉ hiển thị tên',
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

/**
 * Ví dụ cơ bản với tất cả các tính năng mặc định
 */
export const Default: Story = {
  args: {
    label: 'Chọn khoảng ngày',
    placeholder: 'dd/MM/yyyy - dd/MM/yyyy',
    onChange: action('date-range-changed'),
  },
}

/**
 * DateRangePicker với giá trị mặc định
 */
export const WithDefaultValue: Story = {
  args: {
    label: 'Kỳ nghỉ',
    value: {
      from: new Date('2025-05-15'),
      to: new Date('2025-05-29'),
    },
    dateFormat: 'dd/MM/yyyy',
    onChange: action('vacation-range-changed'),
  },
}

/**
 * DateRangePicker với dropdown năm/tháng
 */
export const WithYearNavigation: Story = {
  args: {
    label: 'Với dropdown năm/tháng',
    useYearNavigation: true,
    dateFormat: 'dd/MM/yyyy',
    onChange: action('year-nav-range-changed'),
  },
  parameters: {
    docs: {
      description: {
        story:
          'DateRangePicker với dropdown để chọn năm và tháng, hữu ích khi cần chọn ngày xa.',
      },
    },
  },
}

/**
 * DateRangePicker hiển thị 1 tháng
 */
export const SingleMonth: Story = {
  args: {
    label: 'Chọn khoảng ngày (1 tháng)',
    numberOfMonths: 1,
    dateFormat: 'dd/MM/yyyy',
    onChange: action('single-month-changed'),
  },
}

/**
 * DateRangePicker với giới hạn ngày
 */
export const WithDateLimits: Story = {
  args: {
    label: 'Chọn khoảng ngày (3 tháng tới)',
    minDate: new Date(),
    maxDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
    dateFormat: 'dd/MM/yyyy',
    onChange: action('limited-range-changed'),
  },
}

/**
 * DateRangePicker bắt buộc với validation
 */
export const Required: Story = {
  args: {
    label: 'Khoảng ngày bắt buộc',
    required: true,
    error: 'Vui lòng chọn khoảng ngày',
    dateFormat: 'dd/MM/yyyy',
    onChange: action('required-range-changed'),
  },
}

/**
 * DateRangePicker bị vô hiệu hóa
 */
export const Disabled: Story = {
  args: {
    label: 'Khoảng ngày không thể chỉnh sửa',
    value: {
      from: new Date('2025-05-15'),
      to: new Date('2025-05-29'),
    },
    disabled: true,
    dateFormat: 'dd/MM/yyyy',
    onChange: action('disabled-range-changed'),
  },
}

// Interactive component for testing
const InteractiveComponent = () => {
  const [value, setValue] = React.useState<DateRangeType | undefined>(undefined)

  return (
    <div className='w-full max-w-xl space-y-4'>
      <DateRangePicker
        label='Test Interactive Selection'
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
          action('interactive-changed')(newValue)
        }}
        dateFormat='dd/MM/yyyy'
        numberOfMonths={2}
        useYearNavigation={true}
      />

      <div className='p-4 bg-gray-50 rounded-lg space-y-2'>
        <h3 className='font-semibold text-sm'>Current Value:</h3>
        <p className='text-sm'>
          <strong>From:</strong>{' '}
          {value?.from
            ? value.from.toLocaleDateString('vi-VN')
            : 'Not selected'}
        </p>
        <p className='text-sm'>
          <strong>To:</strong>{' '}
          {value?.to ? value.to.toLocaleDateString('vi-VN') : 'Not selected'}
        </p>
      </div>

      <div className='flex gap-2'>
        <button
          onClick={() => setValue(undefined)}
          className='px-3 py-1.5 bg-red-500 text-white text-sm rounded hover:bg-red-600'
        >
          Clear
        </button>
        <button
          onClick={() => {
            const today = new Date()
            const nextWeek = new Date()
            nextWeek.setDate(today.getDate() + 7)
            setValue({ from: today, to: nextWeek })
          }}
          className='px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600'
        >
          Next 7 Days
        </button>
      </div>
    </div>
  )
}

/**
 * Interactive test với state management
 */
export const Interactive: Story = {
  render: () => <InteractiveComponent />,
  parameters: {
    docs: {
      description: {
        story:
          'Interactive example với state management để test chức năng chọn range.',
      },
    },
  },
}

// Clean test component without initial value
const CleanTestComponent = () => {
  const [value, setValue] = React.useState<DateRangeType | undefined>(undefined)

  return (
    <div className='w-full max-w-xl space-y-4'>
      <DateRangePicker
        label='Clean Test (No Initial Value)'
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
          action('clean-test-changed')(newValue)
        }}
        dateFormat='dd/MM/yyyy'
        numberOfMonths={2}
        useYearNavigation={false}
      />

      <div className='p-4 bg-gray-50 rounded-lg space-y-2'>
        <h3 className='font-semibold text-sm'>Current Value:</h3>
        <p className='text-sm'>
          <strong>From:</strong>{' '}
          {value?.from
            ? value.from.toLocaleDateString('vi-VN')
            : 'Not selected'}
        </p>
        <p className='text-sm'>
          <strong>To:</strong>{' '}
          {value?.to ? value.to.toLocaleDateString('vi-VN') : 'Not selected'}
        </p>
      </div>
    </div>
  )
}

/**
 * Clean test without initial value
 */
export const CleanTest: Story = {
  render: () => <CleanTestComponent />,
  parameters: {
    docs: {
      description: {
        story: 'Clean test component starting with no initial value.',
      },
    },
  },
}

// Test component for range selection behavior
const RangeSelectionTestComponent = () => {
  const [value, setValue] = React.useState<DateRangeType | undefined>(undefined)

  return (
    <div className='w-full max-w-xl space-y-4'>
      <DateRangePicker
        label='Test Range Selection Behavior'
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
          action('range-selection-test')(newValue)
        }}
        dateFormat='dd/MM/yyyy'
        numberOfMonths={2}
        useYearNavigation={false}
      />

      <div className='p-4 bg-blue-50 rounded-lg space-y-2'>
        <h3 className='font-semibold text-sm text-blue-800'>Instructions:</h3>
        <ol className='text-sm text-blue-700 space-y-1'>
          <li>1. Click on a start date</li>
          <li>2. Notice that dates before the start date are now disabled</li>
          <li>3. Hover over future dates to see range preview</li>
          <li>4. Click on an end date to complete the selection</li>
        </ol>
      </div>

      <div className='p-4 bg-gray-50 rounded-lg space-y-2'>
        <h3 className='font-semibold text-sm'>Current Value:</h3>
        <p className='text-sm'>
          <strong>From:</strong>{' '}
          {value?.from
            ? value.from.toLocaleDateString('vi-VN')
            : 'Not selected'}
        </p>
        <p className='text-sm'>
          <strong>To:</strong>{' '}
          {value?.to ? value.to.toLocaleDateString('vi-VN') : 'Not selected'}
        </p>
      </div>
    </div>
  )
}

/**
 * Test range selection behavior
 */
export const RangeSelectionTest: Story = {
  render: () => <RangeSelectionTestComponent />,
  parameters: {
    docs: {
      description: {
        story:
          'Test component để kiểm tra behavior của range selection với disabled dates.',
      },
    },
  },
}
