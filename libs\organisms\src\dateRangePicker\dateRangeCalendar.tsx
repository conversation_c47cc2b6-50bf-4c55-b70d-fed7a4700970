// File: libs/organisms/src/dateRangePicker/dateRangeCalendar.tsx
import { Locale } from 'date-fns'
import React, { useEffect, useState } from 'react'
import { CalendarGrid } from '../date-picker-common/CalendarGrid'
import { CalendarHeader } from '../date-picker-common/CalendarHeader'
import { DateRangeType } from '../date-picker-common/types'
import { useDatePickerBaseLogic } from '../date-picker-common/useDatePickerBaseLogic'

interface DateRangeCalendarProps {
  value?: DateRangeType
  onSelect: (range: DateRangeType | undefined) => void
  numberOfMonths: 1 | 2
  locale: Locale
  minDate?: Date
  maxDate?: Date
  disabled?: (date: Date) => boolean
}

export const DateRangeCalendar: React.FC<DateRangeCalendarProps> = ({
  value,
  onSelect,
  numberOfMonths,
  locale,
  minDate,
  maxDate,
  disabled,
}) => {
  // Independent state for each calendar to avoid sync conflicts
  const [firstCalendarDate, setFirstCalendarDate] = useState(
    () => value?.from || new Date(),
  )

  const [secondCalendarDate, setSecondCalendarDate] = useState(() => {
    const baseDate = value?.from || new Date()
    return new Date(baseDate.getFullYear(), baseDate.getMonth() + 1, 1)
  })

  // Sync state with value when value changes (e.g., when popup opens with existing range)
  useEffect(() => {
    if (value?.from) {
      setFirstCalendarDate(value.from)
      const secondDate = new Date(
        value.from.getFullYear(),
        value.from.getMonth() + 1,
        1,
      )
      setSecondCalendarDate(secondDate)
    }
  }, [value?.from])

  // Custom setYear/setMonth handlers that update our state
  const handleFirstCalendarYearChange = (year: number) => {
    console.log('First calendar year change:', year)
    const newDate = new Date(firstCalendarDate)
    newDate.setFullYear(year)
    setFirstCalendarDate(newDate)
  }

  const handleFirstCalendarMonthChange = (month: number) => {
    console.log('First calendar month change:', month)
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(month)
    setFirstCalendarDate(newDate)
  }

  const handleSecondCalendarYearChange = (year: number) => {
    console.log('Second calendar year change:', year)
    const newDate = new Date(secondCalendarDate)
    newDate.setFullYear(year)
    setSecondCalendarDate(newDate)
  }

  const handleSecondCalendarMonthChange = (month: number) => {
    console.log('Second calendar month change:', month)
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(month)
    setSecondCalendarDate(newDate)
  }

  // Custom prev/next handlers for first calendar
  const handleFirstCalendarPrevMonth = () => {
    console.log('First calendar prev month')
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(newDate.getMonth() - 1)
    setFirstCalendarDate(newDate)
  }

  const handleFirstCalendarNextMonth = () => {
    console.log('First calendar next month')
    const newDate = new Date(firstCalendarDate)
    newDate.setMonth(newDate.getMonth() + 1)
    setFirstCalendarDate(newDate)
  }

  // Custom prev/next handlers for second calendar
  const handleSecondCalendarPrevMonth = () => {
    console.log('Second calendar prev month')
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(newDate.getMonth() - 1)
    setSecondCalendarDate(newDate)
  }

  const handleSecondCalendarNextMonth = () => {
    console.log('Second calendar next month')
    const newDate = new Date(secondCalendarDate)
    newDate.setMonth(newDate.getMonth() + 1)
    setSecondCalendarDate(newDate)
  }

  // Use base logic for the first calendar
  const firstCalendarLogic = useDatePickerBaseLogic({
    value: undefined, // Don't sync with value to avoid conflicts
    minDate,
    maxDate,
    locale,
    useYearNavigation: true,
    numberOfMonths: 1,
    isRangePicker: true,
    initialVisibleDate: firstCalendarDate,
  })

  // Use base logic for the second calendar
  const secondCalendarLogic = useDatePickerBaseLogic({
    value: undefined, // Don't sync with value to avoid conflicts
    minDate,
    maxDate,
    locale,
    useYearNavigation: true,
    numberOfMonths: 1,
    isRangePicker: true,
    initialVisibleDate: secondCalendarDate,
  })

  // Update calendar logic when our state changes
  useEffect(() => {
    firstCalendarLogic.setCurrentDisplayedMonthDate(firstCalendarDate)
  }, [firstCalendarDate])

  useEffect(() => {
    secondCalendarLogic.setCurrentDisplayedMonthDate(secondCalendarDate)
  }, [secondCalendarDate])

  const handleDateSelect = (date: Date) => {
    if (!value?.from || (value.from && value.to)) {
      // Start new selection
      onSelect({ from: date, to: undefined })
    } else if (value.from && !value.to) {
      // Complete the range
      if (date >= value.from) {
        onSelect({ from: value.from, to: date })
      } else {
        onSelect({ from: date, to: value.from })
      }
    }
  }

  const isDateInRange = (date: Date): boolean => {
    if (!value?.from || !value?.to) return false
    return date >= value.from && date <= value.to
  }

  const isDateRangeStart = (date: Date): boolean => {
    if (!value?.from) return false
    return date.getTime() === value.from.getTime()
  }

  const isDateRangeEnd = (date: Date): boolean => {
    if (!value?.to) return false
    return date.getTime() === value.to.getTime()
  }

  const isDateSelected = (date: Date): boolean => {
    return isDateRangeStart(date) || isDateRangeEnd(date)
  }

  // Enhance days with range information
  const enhanceDaysWithRange = (days: any[]) => {
    return days.map((day) => ({
      ...day,
      isSelected: isDateSelected(day.date),
      isInRange: isDateInRange(day.date),
      isRangeStart: isDateRangeStart(day.date),
      isRangeEnd: isDateRangeEnd(day.date),
    }))
  }

  if (numberOfMonths === 1) {
    const monthData = firstCalendarLogic.visibleMonthsData[0]
    const enhancedDays = enhanceDaysWithRange(monthData.days)

    return (
      <div className='space-y-3'>
        <CalendarHeader
          monthName={monthData.monthName}
          year={monthData.year}
          currentMonth={firstCalendarDate.getMonth()}
          currentYear={firstCalendarDate.getFullYear()}
          years={firstCalendarLogic.years}
          months={firstCalendarLogic.months}
          prevMonth={handleFirstCalendarPrevMonth}
          nextMonth={handleFirstCalendarNextMonth}
          setYear={handleFirstCalendarYearChange}
          setMonth={handleFirstCalendarMonthChange}
          useYearNavigation={true}
        />
        <CalendarGrid
          days={enhancedDays}
          onDateSelect={handleDateSelect}
          locale={locale}
        />
      </div>
    )
  }

  // For 2 months
  const firstMonth = firstCalendarLogic.visibleMonthsData[0]
  const secondMonth = secondCalendarLogic.visibleMonthsData[0]

  const firstMonthDays = enhanceDaysWithRange(firstMonth.days)
  const secondMonthDays = enhanceDaysWithRange(secondMonth.days)

  return (
    <div className='space-y-3'>
      <div className='grid grid-cols-2 gap-4'>
        {/* First Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={firstMonth.monthName}
            year={firstMonth.year}
            currentMonth={firstCalendarDate.getMonth()}
            currentYear={firstCalendarDate.getFullYear()}
            years={firstCalendarLogic.years}
            months={firstCalendarLogic.months}
            prevMonth={handleFirstCalendarPrevMonth}
            nextMonth={handleFirstCalendarNextMonth}
            setYear={handleFirstCalendarYearChange}
            setMonth={handleFirstCalendarMonthChange}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={firstMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>

        {/* Second Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={secondMonth.monthName}
            year={secondMonth.year}
            currentMonth={secondCalendarDate.getMonth()}
            currentYear={secondCalendarDate.getFullYear()}
            years={secondCalendarLogic.years}
            months={secondCalendarLogic.months}
            prevMonth={handleSecondCalendarPrevMonth}
            nextMonth={handleSecondCalendarNextMonth}
            setYear={handleSecondCalendarYearChange}
            setMonth={handleSecondCalendarMonthChange}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={secondMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>
      </div>
    </div>
  )
}
