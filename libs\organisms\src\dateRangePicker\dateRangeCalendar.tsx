// File: libs/organisms/src/dateRangePicker/dateRangeCalendar.tsx
import { Locale } from 'date-fns'
import React from 'react'
import { CalendarGrid } from '../date-picker-common/CalendarGrid'
import { CalendarHeader } from '../date-picker-common/CalendarHeader'
import { DateRangeType } from '../date-picker-common/types'
import { useDatePickerBaseLogic } from '../date-picker-common/useDatePickerBaseLogic'

interface DateRangeCalendarProps {
  value?: DateRangeType
  onSelect: (range: DateRangeType | undefined) => void
  numberOfMonths: 1 | 2
  locale: Locale
  minDate?: Date
  maxDate?: Date
  disabled?: (date: Date) => boolean
}

export const DateRangeCalendar: React.FC<DateRangeCalendarProps> = ({
  value,
  onSelect,
  numberOfMonths,
  locale,
  minDate,
  maxDate,
  disabled,
}) => {
  // Use base logic for the first calendar
  const firstCalendarLogic = useDatePickerBaseLogic({
    value: value?.from,
    minDate,
    maxDate,
    locale,
    useYearNavigation: true,
    numberOfMonths: 1,
    isRangePicker: true,
    initialVisibleDate: value?.from || new Date(),
  })

  // Use base logic for the second calendar (one month ahead)
  const secondCalendarLogic = useDatePickerBaseLogic({
    value: value?.from,
    minDate,
    maxDate,
    locale,
    useYearNavigation: true,
    numberOfMonths: 1,
    isRangePicker: true,
    initialVisibleDate: new Date(
      (value?.from || new Date()).getFullYear(),
      (value?.from || new Date()).getMonth() + 1,
      1,
    ),
  })

  const handleDateSelect = (date: Date) => {
    if (!value?.from || (value.from && value.to)) {
      // Start new selection
      onSelect({ from: date, to: undefined })
    } else if (value.from && !value.to) {
      // Complete the range
      if (date >= value.from) {
        onSelect({ from: value.from, to: date })
      } else {
        onSelect({ from: date, to: value.from })
      }
    }
  }

  const isDateInRange = (date: Date): boolean => {
    if (!value?.from || !value?.to) return false
    return date >= value.from && date <= value.to
  }

  const isDateRangeStart = (date: Date): boolean => {
    if (!value?.from) return false
    return date.getTime() === value.from.getTime()
  }

  const isDateRangeEnd = (date: Date): boolean => {
    if (!value?.to) return false
    return date.getTime() === value.to.getTime()
  }

  const isDateSelected = (date: Date): boolean => {
    return isDateRangeStart(date) || isDateRangeEnd(date)
  }

  // Enhance days with range information
  const enhanceDaysWithRange = (days: any[]) => {
    return days.map((day) => ({
      ...day,
      isSelected: isDateSelected(day.date),
      isInRange: isDateInRange(day.date),
      isRangeStart: isDateRangeStart(day.date),
      isRangeEnd: isDateRangeEnd(day.date),
    }))
  }

  if (numberOfMonths === 1) {
    const monthData = firstCalendarLogic.visibleMonthsData[0]
    const enhancedDays = enhanceDaysWithRange(monthData.days)

    return (
      <div className='space-y-3'>
        <CalendarHeader
          monthName={monthData.monthName}
          year={monthData.year}
          currentMonth={firstCalendarLogic.currentMonth}
          currentYear={firstCalendarLogic.currentYear}
          years={firstCalendarLogic.years}
          months={firstCalendarLogic.months}
          prevMonth={firstCalendarLogic.prevMonth}
          nextMonth={firstCalendarLogic.nextMonth}
          setYear={firstCalendarLogic.setYear}
          setMonth={firstCalendarLogic.setMonth}
          useYearNavigation={true}
        />
        <CalendarGrid
          days={enhancedDays}
          onDateSelect={handleDateSelect}
          locale={locale}
        />
      </div>
    )
  }

  // For 2 months
  const firstMonth = firstCalendarLogic.visibleMonthsData[0]
  const secondMonth = secondCalendarLogic.visibleMonthsData[0]

  console.log('First month data:', {
    monthName: firstMonth.monthName,
    year: firstMonth.year,
    currentMonth: firstCalendarLogic.currentMonth,
    currentYear: firstCalendarLogic.currentYear,
  })

  console.log('Second month data:', {
    monthName: secondMonth.monthName,
    year: secondMonth.year,
    currentMonth: secondCalendarLogic.currentMonth,
    currentYear: secondCalendarLogic.currentYear,
  })

  const firstMonthDays = enhanceDaysWithRange(firstMonth.days)
  const secondMonthDays = enhanceDaysWithRange(secondMonth.days)

  return (
    <div className='space-y-3'>
      <div className='grid grid-cols-2 gap-4'>
        {/* First Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={firstMonth.monthName}
            year={firstMonth.year}
            currentMonth={firstCalendarLogic.currentMonth}
            currentYear={firstCalendarLogic.currentYear}
            years={firstCalendarLogic.years}
            months={firstCalendarLogic.months}
            prevMonth={firstCalendarLogic.prevMonth}
            nextMonth={firstCalendarLogic.nextMonth}
            setYear={(year) => {
              console.log('First calendar setYear:', year)
              firstCalendarLogic.setYear(year)
            }}
            setMonth={(month) => {
              console.log('First calendar setMonth:', month)
              firstCalendarLogic.setMonth(month)
            }}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={firstMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>

        {/* Second Month */}
        <div className='space-y-3'>
          <CalendarHeader
            monthName={secondMonth.monthName}
            year={secondMonth.year}
            currentMonth={secondCalendarLogic.currentMonth}
            currentYear={secondCalendarLogic.currentYear}
            years={secondCalendarLogic.years}
            months={secondCalendarLogic.months}
            prevMonth={secondCalendarLogic.prevMonth}
            nextMonth={secondCalendarLogic.nextMonth}
            setYear={(year) => {
              console.log('Second calendar setYear:', year)
              secondCalendarLogic.setYear(year)
            }}
            setMonth={(month) => {
              console.log('Second calendar setMonth:', month)
              secondCalendarLogic.setMonth(month)
            }}
            useYearNavigation={true}
          />
          <CalendarGrid
            days={secondMonthDays}
            onDateSelect={handleDateSelect}
            locale={locale}
          />
        </div>
      </div>
    </div>
  )
}
